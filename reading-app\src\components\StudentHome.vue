<template>
  <view class="student-home">
    <!-- 用户欢迎卡片 -->
    <view class="welcome-card">
      <view class="welcome-bg">
        <view class="bg-decoration">
          <view class="decoration-circle circle-1"></view>
          <view class="decoration-circle circle-2"></view>
          <view class="decoration-star star-1">⭐</view>
          <view class="decoration-star star-2">✨</view>
        </view>
        <view class="welcome-content">
          <view class="user-info">
            <view class="avatar-container" @click="handleAvatarClick">
              <wd-img
                :src="userStore.avatarUrl || '/static/images/default-avatar.png'"
                width="70"
                height="70"
                round
                class="user-avatar"
              />
              <view class="notification-dot" v-if="hasNotification"></view>
            </view>
            <view class="user-text">
              <text class="greeting">{{ greeting }}{{ userStore.nickname || '小朋友' }}</text>
              <text class="welcome-desc">今天也要好好学习哦！</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 今日学习目标 -->
    <view class="daily-goal-section">
      <view class="goal-card">
        <view class="goal-header">
          <text class="goal-title">今日学习目标</text>
          <text class="goal-date">{{ todayDate }}</text>
        </view>
        <view class="goal-progress">
          <view class="progress-circle">
            <view class="circle-bg">
              <view class="circle-fill" :style="{ transform: `rotate(${dailyProgress * 3.6}deg)` }"></view>
            </view>
            <text class="progress-text">{{ dailyProgress }}%</text>
          </view>
          <view class="goal-details">
            <view class="goal-item">
              <text class="goal-label">阅读绘本</text>
              <text class="goal-value">{{ completedBooks }}/3 本</text>
            </view>
            <view class="goal-item">
              <text class="goal-label">完成作业</text>
              <text class="goal-value">{{ completedHomework }}/2 项</text>
            </view>
            <view class="goal-item">
              <text class="goal-label">听力练习</text>
              <text class="goal-value">{{ completedListening }}/1 次</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 中英文书架 -->
    <view class="bookshelf-section">
      <view class="section-title">
        <text class="title-text">我的书架</text>
        <text class="title-desc">选择语言开始阅读</text>
      </view>
      <view class="bookshelf-tabs">
        <view class="bookshelf-tab english" @click="switchToTab('/pages/english/index')">
          <view class="tab-icon">🇺🇸</view>
          <view class="tab-content">
            <text class="tab-title">English Books</text>
            <text class="tab-desc">英文绘本 · 提升英语水平</text>
            <text class="tab-count">{{ englishBookCount }} 本</text>
          </view>
          <view class="tab-arrow">
            <wd-icon name="arrow-right" size="16" />
          </view>
        </view>
        <view class="bookshelf-tab chinese" @click="switchToTab('/pages/chinese/index')">
          <view class="tab-icon">🇨🇳</view>
          <view class="tab-content">
            <text class="tab-title">中文绘本</text>
            <text class="tab-desc">Chinese Books · 培养阅读习惯</text>
            <text class="tab-count">{{ chineseBookCount }} 本</text>
          </view>
          <view class="tab-arrow">
            <wd-icon name="arrow-right" size="16" />
          </view>
        </view>
      </view>
    </view>

    <!-- 快速入口 -->
    <view class="quick-entry-section">
      <view class="section-title">
        <text class="title-text">学习工具</text>
      </view>
      <view class="entry-grid">
        <view class="entry-card urgent" @click="navigateTo('/pages/homework/index')" v-if="pendingHomework > 0">
          <view class="entry-icon">📝</view>
          <text class="entry-title">待完成作业</text>
          <text class="entry-badge">{{ pendingHomework }}</text>
        </view>
        <view class="entry-card" @click="navigateTo('/pages/listening/index')">
          <view class="entry-icon">🎧</view>
          <text class="entry-title">磨耳朵</text>
        </view>
        <view class="entry-card" @click="navigateTo('/pages/collection/index')">
          <view class="entry-icon">⭐</view>
          <text class="entry-title">我的收藏</text>
        </view>
        <view class="entry-card" @click="navigateTo('/pages/checkin/index')">
          <view class="entry-icon">📅</view>
          <text class="entry-title">学习打卡</text>
        </view>
      </view>
    </view>

    <!-- 分类导航 -->
    <view class="category-section">
      <view class="section-title">
        <text class="title-text">精选分类</text>
        <text class="title-desc">选择你喜欢的故事类型</text>
      </view>
      <view class="category-grid">
        <view class="category-card" @click="navigateToCategory('animals')">
          <view class="category-icon animals">🐻</view>
          <text class="category-name">动物故事</text>
          <text class="category-count">12本</text>
        </view>
        <view class="category-card" @click="navigateToCategory('fairy')">
          <view class="category-icon fairy">🧚‍♀️</view>
          <text class="category-name">童话故事</text>
          <text class="category-count">15本</text>
        </view>
        <view class="category-card" @click="navigateToCategory('science')">
          <view class="category-icon science">🔬</view>
          <text class="category-name">科学探索</text>
          <text class="category-count">8本</text>
        </view>
        <view class="category-card" @click="navigateToCategory('life')">
          <view class="category-icon life">🏠</view>
          <text class="category-name">生活常识</text>
          <text class="category-count">10本</text>
        </view>
      </view>
    </view>

    <!-- 推荐绘本 -->
    <view class="recommend-section">
      <view class="section-title">
        <text class="title-text">今日推荐</text>
        <text class="view-all" @click="switchToTab('/pages/english/index')">查看更多</text>
      </view>
      <scroll-view class="book-scroll" scroll-x show-scrollbar="false">
        <view class="book-list">
          <view 
            class="book-card" 
            v-for="book in recommendBooks" 
            :key="book.id"
            @click="navigateTo(`/pages/reader/index?bookId=${book.id}`)"
          >
            <view class="book-cover-container">
              <image :src="book.coverUrl" class="book-cover" mode="aspectFill" />
              <view class="book-badge" v-if="book.isNew">NEW</view>
            </view>
            <view class="book-info">
              <text class="book-title">{{ book.title }}</text>
              <text class="book-level">{{ book.level }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 最近阅读 -->
    <view class="recent-section">
      <view class="section-title">
        <text class="title-text">继续阅读</text>
        <text class="view-all" @click="navigateTo('/pages/reading-history/index')">查看全部</text>
      </view>
      <view class="recent-list">
        <view 
          class="recent-item" 
          v-for="book in recentBooks" 
          :key="book.id"
          @click="navigateTo(`/pages/reader/index?bookId=${book.id}`)"
        >
          <image :src="book.coverUrl" class="recent-cover" mode="aspectFill" />
          <view class="recent-info">
            <text class="recent-title">{{ book.title }}</text>
            <text class="recent-progress">已读 {{ book.progress }}%</text>
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: book.progress + '%' }"></view>
            </view>
          </view>
          <view class="continue-btn">
            <text>继续</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 学习成就 -->
    <view class="achievement-section">
      <view class="section-title">
        <text class="title-text">学习成就</text>
      </view>
      <view class="achievement-card">
        <view class="achievement-bg">
          <view class="achievement-decoration">
            <text class="decoration-emoji">🏆</text>
          </view>
          <view class="achievement-stats">
            <view class="stat-row">
              <view class="stat-item">
                <text class="stat-number">{{ userStore.stats?.readDays || 0 }}</text>
                <text class="stat-label">阅读天数</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{ userStore.stats?.readBooks || 0 }}</text>
                <text class="stat-label">完成绘本</text>
              </view>
              <view class="stat-item">
                <text class="stat-number">{{ userStore.stats?.continuousDays || 0 }}</text>
                <text class="stat-label">连续打卡</text>
              </view>
            </view>
            <view class="achievement-badge">
              <text class="badge-text">阅读小达人</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import type { BookInfo } from '@/types'

// Props
interface Props {
  recommendBooks?: BookInfo[]
  recentBooks?: BookInfo[]
}

const props = withDefaults(defineProps<Props>(), {
  recommendBooks: () => [],
  recentBooks: () => []
})

// Emits
interface Emits {
  (e: 'navigate', path: string): void
  (e: 'navigateToCategory', categoryId: string): void
  (e: 'handleAvatarClick'): void
}

const emit = defineEmits<Emits>()

// Store
const userStore = useUserStore()

// 响应式数据
const hasNotification = ref(true)
const completedBooks = ref(1)
const completedHomework = ref(0)
const completedListening = ref(0)
const pendingHomework = ref(2)
const englishBookCount = ref(25)
const chineseBookCount = ref(18)

// 计算属性
const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好，'
  if (hour < 18) return '下午好，'
  return '晚上好，'
})

const dailyProgress = computed(() => {
  const total = 6 // 总目标：3本书 + 2项作业 + 1次听力
  const completed = completedBooks.value + completedHomework.value + completedListening.value
  return Math.round((completed / total) * 100)
})

const todayDate = computed(() => {
  const today = new Date()
  const month = today.getMonth() + 1
  const date = today.getDate()
  return `${month}月${date}日`
})

// 方法
const navigateTo = (path: string) => {
  emit('navigate', path)
}

const navigateToCategory = (categoryId: string) => {
  emit('navigateToCategory', categoryId)
}

const switchToTab = (path: string) => {
  uni.switchTab({
    url: path
  })
}

const handleAvatarClick = () => {
  emit('handleAvatarClick')
}
</script>

<style lang="scss" scoped>
@import '../styles/variables.scss';

.student-home {
  .welcome-card {
    margin-bottom: $spacing-xl;

    .welcome-bg {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 24rpx;
      padding: $spacing-xl;
      position: relative;
      overflow: hidden;
      box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);

      .bg-decoration {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;

        .decoration-circle {
          position: absolute;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.1);

          &.circle-1 {
            width: 120rpx;
            height: 120rpx;
            top: -60rpx;
            right: -60rpx;
          }

          &.circle-2 {
            width: 80rpx;
            height: 80rpx;
            bottom: -40rpx;
            left: -40rpx;
          }
        }

        .decoration-star {
          position: absolute;
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.6);
          animation: twinkle 2s infinite;

          &.star-1 {
            top: 40rpx;
            right: 80rpx;
          }

          &.star-2 {
            bottom: 60rpx;
            right: 40rpx;
            animation-delay: 1s;
          }
        }
      }

      .welcome-content {
        position: relative;
        z-index: 2;

        .user-info {
          display: flex;
          align-items: center;
          gap: $spacing-md;

          .avatar-container {
            position: relative;
            cursor: pointer;

            .user-avatar {
              border: 4rpx solid rgba(255, 255, 255, 0.3);
              box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
            }

            .notification-dot {
              position: absolute;
              top: 8rpx;
              right: 8rpx;
              width: 16rpx;
              height: 16rpx;
              background: #ff4757;
              border-radius: 50%;
              border: 2rpx solid white;
              animation: pulse 2s infinite;
            }
          }

          .user-text {
            flex: 1;

            .greeting {
              display: block;
              font-size: $font-size-xl;
              font-weight: $font-weight-bold;
              color: white;
              margin-bottom: 8rpx;
            }

            .welcome-desc {
              font-size: $font-size-md;
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }
      }
    }
  }

  .daily-goal-section {
    margin-bottom: $spacing-xl;

    .goal-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 24rpx;
      padding: $spacing-xl;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .goal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-lg;

        .goal-title {
          font-size: $font-size-xl;
          font-weight: $font-weight-bold;
          color: $text-color-primary;
        }

        .goal-date {
          font-size: $font-size-sm;
          color: $text-color-secondary;
          background: #f0f0f0;
          padding: 8rpx 16rpx;
          border-radius: 12rpx;
        }
      }

      .goal-progress {
        display: flex;
        align-items: center;
        gap: $spacing-xl;

        .progress-circle {
          position: relative;
          width: 120rpx;
          height: 120rpx;

          .circle-bg {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(#667eea 0deg, #f0f0f0 0deg);
            position: relative;

            &::after {
              content: '';
              position: absolute;
              top: 12rpx;
              left: 12rpx;
              width: 96rpx;
              height: 96rpx;
              background: white;
              border-radius: 50%;
            }
          }

          .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: $font-size-lg;
            font-weight: $font-weight-bold;
            color: #667eea;
            z-index: 2;
          }
        }

        .goal-details {
          flex: 1;

          .goal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .goal-label {
              font-size: $font-size-md;
              color: $text-color-secondary;
            }

            .goal-value {
              font-size: $font-size-md;
              font-weight: $font-weight-medium;
              color: $text-color-primary;
            }
          }
        }
      }
    }
  }

  .bookshelf-section {
    margin-bottom: $spacing-xl;

    .section-title {
      margin-bottom: $spacing-lg;

      .title-text {
        display: block;
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: white;
        margin-bottom: 8rpx;
      }

      .title-desc {
        font-size: $font-size-sm;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .bookshelf-tabs {
      display: flex;
      flex-direction: column;
      gap: $spacing-md;

      .bookshelf-tab {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20rpx;
        padding: $spacing-lg;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: $spacing-md;

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
        }

        &.english {
          border-left: 6rpx solid #667eea;

          .tab-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
        }

        &.chinese {
          border-left: 6rpx solid #ff6b6b;

          .tab-icon {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          }
        }

        .tab-icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 36rpx;
        }

        .tab-content {
          flex: 1;

          .tab-title {
            display: block;
            font-size: $font-size-lg;
            font-weight: $font-weight-bold;
            color: $text-color-primary;
            margin-bottom: 8rpx;
          }

          .tab-desc {
            display: block;
            font-size: $font-size-sm;
            color: $text-color-secondary;
            margin-bottom: 8rpx;
          }

          .tab-count {
            font-size: $font-size-sm;
            color: #667eea;
            font-weight: $font-weight-medium;
            background: rgba(102, 126, 234, 0.1);
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
            display: inline-block;
          }
        }

        .tab-arrow {
          color: $text-color-secondary;
        }
      }
    }
  }

  .quick-entry-section {
    margin-bottom: $spacing-xl;

    .section-title {
      margin-bottom: $spacing-lg;

      .title-text {
        display: block;
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: white;
        margin-bottom: 8rpx;
      }
    }

    .entry-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-md;

      .entry-card {
        position: relative;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20rpx;
        padding: $spacing-lg;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

        &:active {
          transform: translateY(2rpx);
        }

        &.urgent {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);

          .entry-icon,
          .entry-title {
            color: white;
          }
        }

        .entry-icon {
          font-size: 48rpx;
          margin-bottom: $spacing-md;
          display: block;
        }

        .entry-title {
          display: block;
          font-size: $font-size-md;
          font-weight: $font-weight-medium;
          color: $text-color-primary;
        }

        .entry-badge {
          position: absolute;
          top: -8rpx;
          right: -8rpx;
          background: #ff4757;
          color: white;
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 12rpx;
          min-width: 24rpx;
          text-align: center;
        }
      }
    }
  }

  .category-section {
    margin-bottom: $spacing-xl;

    .section-title {
      margin-bottom: $spacing-lg;

      .title-text {
        display: block;
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: white;
        margin-bottom: 8rpx;
      }

      .title-desc {
        font-size: $font-size-sm;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .category-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-md;

      .category-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20rpx;
        padding: $spacing-lg;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

        &:active {
          transform: translateY(2rpx);
        }

        .category-icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto $spacing-sm;
          font-size: 36rpx;

          &.animals {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
          }

          &.fairy {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
          }

          &.science {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.life {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
          }
        }

        .category-name {
          display: block;
          font-size: $font-size-md;
          font-weight: $font-weight-medium;
          color: $text-color-primary;
          margin-bottom: $spacing-sm;
        }

        .category-count {
          font-size: $font-size-sm;
          color: $text-color-secondary;
        }
      }
    }
  }

  .recommend-section {
    margin-bottom: $spacing-xl;

    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-lg;

      .title-text {
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: white;
      }

      .view-all {
        font-size: $font-size-sm;
        color: #ffd93d;
        cursor: pointer;
      }
    }

    .book-scroll {
      .book-list {
        display: flex;
        gap: $spacing-md;
        padding: 0 $spacing-xs;

        .book-card {
          min-width: 140rpx;
          cursor: pointer;

          .book-cover-container {
            position: relative;
            margin-bottom: $spacing-sm;

            .book-cover {
              width: 140rpx;
              height: 180rpx;
              border-radius: 16rpx;
              box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
              background: white;
            }

            .book-badge {
              position: absolute;
              top: 8rpx;
              right: 8rpx;
              background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
              color: white;
              font-size: 20rpx;
              padding: 4rpx 8rpx;
              border-radius: 8rpx;
              font-weight: $font-weight-medium;
            }
          }

          .book-info {
            .book-title {
              display: block;
              font-size: $font-size-sm;
              font-weight: $font-weight-medium;
              color: white;
              margin-bottom: 4rpx;
              width: 140rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .book-level {
              font-size: 22rpx;
              color: rgba(255, 255, 255, 0.7);
              background: rgba(255, 255, 255, 0.2);
              padding: 2rpx 8rpx;
              border-radius: 8rpx;
              display: inline-block;
            }
          }
        }
      }
    }
  }

  .recent-section {
    margin-bottom: $spacing-xl;

    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-lg;

      .title-text {
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: white;
      }

      .view-all {
        font-size: $font-size-sm;
        color: #ffd93d;
        cursor: pointer;
      }
    }

    .recent-list {
      .recent-item {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20rpx;
        padding: $spacing-md;
        margin-bottom: $spacing-md;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

        &:active {
          transform: translateY(2rpx);
        }

        .recent-cover {
          width: 80rpx;
          height: 100rpx;
          border-radius: 12rpx;
          margin-right: $spacing-md;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        }

        .recent-info {
          flex: 1;

          .recent-title {
            display: block;
            font-size: $font-size-md;
            font-weight: $font-weight-medium;
            color: $text-color-primary;
            margin-bottom: 8rpx;
          }

          .recent-progress {
            font-size: $font-size-sm;
            color: $text-color-secondary;
            margin-bottom: $spacing-sm;
          }

          .progress-bar {
            width: 100%;
            height: 6rpx;
            background: #f0f0f0;
            border-radius: 3rpx;
            overflow: hidden;

            .progress-fill {
              height: 100%;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border-radius: 3rpx;
              transition: width 0.3s ease;
            }
          }
        }

        .continue-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 12rpx 20rpx;
          border-radius: 20rpx;
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
        }
      }
    }
  }

  .achievement-section {
    margin-bottom: $spacing-xl;

    .section-title {
      margin-bottom: $spacing-lg;

      .title-text {
        display: block;
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: white;
        margin-bottom: 8rpx;
      }
    }

    .achievement-card {
      .achievement-bg {
        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
        border-radius: 20rpx;
        padding: $spacing-xl;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8rpx 32rpx rgba(250, 177, 160, 0.3);

        .achievement-decoration {
          position: absolute;
          top: $spacing-md;
          right: $spacing-md;

          .decoration-emoji {
            font-size: 48rpx;
            opacity: 0.8;
          }
        }

        .achievement-stats {
          .stat-row {
            display: flex;
            gap: $spacing-xl;
            margin-bottom: $spacing-lg;

            .stat-item {
              text-align: center;

              .stat-number {
                display: block;
                font-size: $font-size-xxl;
                font-weight: $font-weight-bold;
                color: white;
                margin-bottom: 4rpx;
              }

              .stat-label {
                font-size: $font-size-sm;
                color: rgba(255, 255, 255, 0.9);
              }
            }
          }

          .achievement-badge {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20rpx;
            padding: 12rpx 24rpx;
            display: inline-block;

            .badge-text {
              font-size: $font-size-md;
              font-weight: $font-weight-medium;
              color: white;
            }
          }
        }
      }
    }
  }

  // 动画效果
  @keyframes twinkle {
    0%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 0.5;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
}
</style>
