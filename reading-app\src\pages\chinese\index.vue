<template>
  <view class="chinese-page">
    <!-- 搜索栏 -->
    <view class="search-section">
      <wd-search
        v-model="searchKeyword"
        placeholder="搜索中文绘本"
        @search="handleSearch"
      />
    </view>

    <!-- 分类标签 -->
    <view class="category-section">
      <scroll-view class="category-scroll" scroll-x show-scrollbar="false">
        <view class="category-list">
          <view
            class="category-item"
            :class="{ active: activeCategory === category.id }"
            v-for="category in categories"
            :key="category.id"
            @click="selectCategory(category.id)"
          >
            <text class="category-text">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 绘本列表 -->
    <view class="book-list-section">
      <z-paging
        ref="paging"
        v-model="bookList"
        @query="queryList"
        :refresher-enabled="true"
        :loading-more-enabled="true"
      >
        <view class="book-grid">
          <view
            class="book-item"
            v-for="book in bookList"
            :key="book.id"
            @click="openBook(book)"
          >
            <view class="book-cover-container">
              <image :src="book.coverUrl" class="book-cover" mode="aspectFill" />
              <view class="book-badge" v-if="book.isNew">新</view>
            </view>
            <view class="book-info">
              <text class="book-title">{{ book.title }}</text>
              <text class="book-desc">{{ book.description }}</text>
            </view>
          </view>
        </view>
      </z-paging>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { bookApi } from '@/api'
import type { BookInfo } from '@/types'

// 响应式数据
const searchKeyword = ref('')
const activeCategory = ref('all')
const bookList = ref<BookInfo[]>([])
const paging = ref()

// 分类数据
const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'story', name: '故事绘本' },
  { id: 'science', name: '科普知识' },
  { id: 'classic', name: '经典童话' },
  { id: 'culture', name: '传统文化' }
])

// 页面生命周期
onMounted(() => {
  loadCategories()
})

// 方法
const loadCategories = async () => {
  try {
    const response = await bookApi.getBookCategories('zh')
    if (response && response.length > 0) {
      categories.value = [
        { id: 'all', name: '全部' },
        ...response.map(cat => ({ id: cat.categoryId, name: cat.name }))
      ]
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const queryList = async (pageNo: number, pageSize: number) => {
  try {
    // 模拟数据
    const mockBooks: BookInfo[] = [
      {
        id: '1',
        title: '小红帽',
        coverUrl: '/static/images/book1.jpg',
        level: '适合3-6岁',
        description: '经典童话故事',
        progress: 0,
        isNew: true
      },
      {
        id: '2',
        title: '三只小猪',
        coverUrl: '/static/images/book2.jpg',
        level: '适合4-7岁',
        description: '寓教于乐的故事',
        progress: 0,
        isNew: false
      }
    ]

    paging.value.complete(mockBooks)
  } catch (error) {
    console.error('加载绘本列表失败:', error)
    paging.value.complete([])
  }
}

const selectCategory = (categoryId: string) => {
  activeCategory.value = categoryId
  paging.value.reload()
}

const handleSearch = () => {
  paging.value.reload()
}

const openBook = (book: BookInfo) => {
  uni.navigateTo({
    url: `/subpages/bookshelf/reader/index?bookId=${book.id}`
  })
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.chinese-page {
  background: #f8f9fa;
  min-height: 100vh;

  .search-section {
    padding: $spacing-md $spacing-lg;
    background: white;
    border-bottom: 1rpx solid #eee;
  }

  .category-section {
    background: white;
    padding: $spacing-md 0;
    border-bottom: 1rpx solid #eee;

    .category-scroll {
      white-space: nowrap;
    }

    .category-list {
      display: flex;
      padding: 0 $spacing-lg;
      gap: $spacing-md;

      .category-item {
        flex-shrink: 0;
        padding: 12rpx 24rpx;
        background: #f5f5f5;
        border-radius: 20rpx;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);

          .category-text {
            color: white;
          }
        }

        .category-text {
          font-size: $font-size-sm;
          color: #666;
          font-weight: $font-weight-medium;
        }
      }
    }
  }

  .book-list-section {
    flex: 1;
    padding: $spacing-md;

    .book-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-md;

      .book-item {
        background: white;
        border-radius: 16rpx;
        overflow: hidden;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
        }

        .book-cover-container {
          position: relative;
          width: 100%;
          height: 240rpx;

          .book-cover {
            width: 100%;
            height: 100%;
          }

          .book-badge {
            position: absolute;
            top: 12rpx;
            right: 12rpx;
            background: #ff6b6b;
            color: white;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
            font-size: 20rpx;
          }
        }

        .book-info {
          padding: $spacing-md;

          .book-title {
            display: block;
            font-size: $font-size-md;
            font-weight: $font-weight-bold;
            color: #333;
            margin-bottom: 8rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .book-desc {
            font-size: $font-size-sm;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
