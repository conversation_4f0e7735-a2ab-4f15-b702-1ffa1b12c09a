<template>
  <view class="index-page">
    <!-- 顶部工具栏 -->
    <view class="top-toolbar">
      <view class="toolbar-left">
        <view class="role-switcher" @click="showRoleSelector" v-if="userStore.hasMultipleRoles">
          <text class="role-text">{{ userStore.roleDisplayName }}</text>
          <wd-icon name="arrow-down" size="14" />
        </view>
      </view>
      <view class="toolbar-right">
        <view class="nav-icon" @click="handleSearch">
          <wd-icon name="search" size="20" />
        </view>
        <view class="nav-icon" @click="handleNotification">
          <wd-icon name="bell" size="20" />
          <view class="notification-dot" v-if="hasNotification"></view>
        </view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 调试信息 -->
      <view class="debug-info" v-if="showDebugInfo" style="background: rgba(255,255,255,0.9); padding: 20rpx; margin: 20rpx; border-radius: 10rpx;">
        <text style="display: block; color: #333;">当前角色: {{ userStore.currentRole || '未设置' }}</text>
        <text style="display: block; color: #333;">是否学生: {{ userStore.isStudent }}</text>
        <text style="display: block; color: #333;">是否教师: {{ userStore.isTeacher }}</text>
        <text style="display: block; color: #333;">是否家长: {{ userStore.isParent }}</text>
      </view>

      <!-- 学生首页 -->
      <StudentHome 
        v-if="userStore.isStudent || !userStore.currentRole"
        :recommendBooks="recommendBooks"
        :recentBooks="recentBooks"
        @navigate="navigateTo"
        @navigateToCategory="navigateToCategory"
        @handleAvatarClick="handleAvatarClick"
      />

      <!-- 教师首页 -->
      <TeacherHome 
        v-else-if="userStore.isTeacher"
        @navigate="navigateTo"
        @handlePendingItem="handlePendingItem"
      />

      <!-- 家长首页 -->
      <ParentHome
        v-else-if="userStore.isParent"
        @navigate="navigateTo"
      />
    </view>

    <!-- 角色选择器 -->
    <RoleSelector
      v-model="showRoleSelectorModal"
      @confirm="onRoleSelected"
      @cancel="onRoleSelectorCancel"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { toast } from '@/utils/loading'
import type { BookInfo, UserRole } from '@/types'
import RoleSelector from '@/components/RoleSelector.vue'
import StudentHome from '@/components/StudentHome.vue'
import TeacherHome from '@/components/TeacherHome.vue'
import ParentHome from '@/components/ParentHome.vue'

// 状态管理
const userStore = useUserStore()

// 响应式数据
const recommendBooks = ref<BookInfo[]>([])
const recentBooks = ref<BookInfo[]>([])
const hasNotification = ref(true)
const showRoleSelectorModal = ref(false)
const showDebugInfo = ref(false) // 临时调试开关

// 页面生命周期
onMounted(async () => {
  // 获取系统信息
  try {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight || 0
  } catch (error) {
    console.warn('获取系统信息失败:', error)
  }

  // 初始化默认角色（用于演示）
  userStore.initDefaultRoles()

  // 加载推荐内容
  await loadRecommendBooks()

  // 加载最近阅读
  await loadRecentBooks()

  // 加载用户统计
  if (userStore.isLoggedIn) {
    try {
      await userStore.getUserStats()
    } catch (error) {
      console.warn('获取用户统计失败，跳过:', error)
    }
  }
})

/**
 * 加载推荐绘本
 */
async function loadRecommendBooks() {
  try {
    // 模拟数据
    recommendBooks.value = [
      {
        id: '1',
        title: 'The Little Red Hen',
        coverUrl: '/static/images/book1.jpg',
        level: 'Level 1',
        isNew: true
      },
      {
        id: '2',
        title: 'Goldilocks',
        coverUrl: '/static/images/book2.jpg',
        level: 'Level 2',
        isNew: false
      }
    ]
  } catch (error) {
    console.error('加载推荐绘本失败:', error)
  }
}

/**
 * 加载最近阅读
 */
async function loadRecentBooks() {
  try {
    // 模拟数据
    recentBooks.value = [
      {
        id: '1',
        title: 'The Three Little Pigs',
        coverUrl: '/static/images/book3.jpg',
        progress: 65
      },
      {
        id: '2',
        title: 'Cinderella',
        coverUrl: '/static/images/book4.jpg',
        progress: 30
      }
    ]
  } catch (error) {
    console.error('加载最近阅读失败:', error)
  }
}

/**
 * 导航到指定页面
 */
function navigateTo(path: string) {
  uni.navigateTo({
    url: path
  })
}

/**
 * 导航到分类页面
 */
function navigateToCategory(categoryId: string) {
  uni.navigateTo({
    url: `/pages/category/index?categoryId=${categoryId}`
  })
}

/**
 * 处理头像点击
 */
function handleAvatarClick() {
  uni.navigateTo({
    url: '/pages/profile/index'
  })
}

/**
 * 处理搜索
 */
function handleSearch() {
  uni.navigateTo({
    url: '/pages/search/index'
  })
}

/**
 * 处理通知
 */
function handleNotification() {
  uni.navigateTo({
    url: '/pages/notification/index'
  })
}

/**
 * 显示角色选择器
 */
function showRoleSelector() {
  showRoleSelectorModal.value = true
}

/**
 * 角色选择确认
 */
function onRoleSelected(role: UserRole) {
  userStore.switchRole(role)
  uni.showToast({
    title: `已切换到${userStore.roleDisplayName}身份`,
    icon: 'success'
  })
}

/**
 * 角色选择取消
 */
function onRoleSelectorCancel() {
  // 取消选择，不做任何操作
}

/**
 * 处理待处理事项点击
 */
function handlePendingItem(item: any) {
  switch (item.type) {
    case 'assignment':
      navigateTo('/pages/assignment/review')
      break
    case 'help':
      navigateTo('/pages/student/help')
      break
    default:
      console.log('处理待处理事项:', item)
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.index-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.top-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md $spacing-lg;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin: $spacing-md;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  
  .toolbar-left {
    .role-switcher {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 20rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 20rpx;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      .role-text {
        font-size: $font-size-sm;
        color: white;
        font-weight: $font-weight-bold;
      }
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: $spacing-md;

    .nav-icon {
      position: relative;
      width: 44rpx;
      height: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 22rpx;
      background: rgba(102, 126, 234, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.9);
        background: rgba(102, 126, 234, 0.2);
      }

      .notification-dot {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        width: 12rpx;
        height: 12rpx;
        background: #ff4757;
        border-radius: 50%;
        border: 2rpx solid white;
      }
    }
  }
}

.page-content {
  padding: $spacing-lg;
}
</style>
